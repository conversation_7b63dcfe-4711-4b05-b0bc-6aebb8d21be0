import { z } from 'zod'

const RuntimeConfigSchema = z.object({
  public: z.object({
    oauthClientId: z.string().min(1, 'Missing NUXT_PUBLIC_OAUTH_CLIENT_ID'),
    devHost: z.string().optional(),
    testHostName: z.string().optional(),
    nodeEnv: z.enum(['development', 'production', 'test'], {
      errorMap: () => ({ message: 'Invalid NODE_ENV' }),
    }),
  }),
})

export function useSafeConfig() {
  const config = useRuntimeConfig()
  return RuntimeConfigSchema.parse(config)
}
