import type { Order } from '~/types/order/order.type'

export const mockOrders: Order[] = [
  {
    id: '#3066',
    client: {
      name: 'Mevr. <PERSON><PERSON>',
      department: 'Afdeling Bos',
      residence: 'Residentie Reynaert',
    },
    contents: {
      count: 2,
      description: 'products',
    },
    date: 'Jan 7, 2025',
    status: 'Submitted',
  },
  {
    id: '#3066',
    client: {
      name: 'Mevr<PERSON> <PERSON><PERSON>',
      department: 'Afdeling Bos',
      residence: 'Residentie Reynaert',
    },
    contents: {
      count: 2,
      description: 'products',
    },
    date: 'Jan 7, 2025',
    status: 'Submitted',
  },
  {
    id: '#3066',
    client: {
      name: 'Mevr. <PERSON><PERSON>',
      department: 'Afdeling Bos',
      residence: 'Residentie Reynaert',
    },
    contents: {
      count: 2,
      description: 'products',
    },
    date: 'Jan 7, 2025',
    status: 'Completed',
  },
  {
    id: '#3066',
    client: {
      name: '<PERSON>v<PERSON><PERSON> <PERSON><PERSON>',
      department: 'Afdeling Bos',
      residence: 'Residentie <PERSON>',
    },
    contents: {
      count: 2,
      description: 'products',
    },
    date: 'Jan 7, 2025',
    status: 'Completed',
  },
  {
    id: '#3066',
    client: {
      name: 'Mevr. <PERSON>. <PERSON>ruers',
      department: 'Afdeling Bos',
      residence: 'Residentie Reynaert',
    },
    contents: {
      count: 2,
      description: 'products',
    },
    date: 'Jan 7, 2025',
    status: 'Completed',
  },
  {
    id: '#3066',
    client: {
      name: 'Mevr. L. Bruers',
      department: 'Afdeling Bos',
      residence: 'Residentie Reynaert',
    },
    contents: {
      count: 2,
      description: 'products',
    },
    date: 'Jan 7, 2025',
    status: 'Completed',
  },
  {
    id: '#3066',
    client: {
      name: 'Mevr. L. Bruers',
      department: 'Afdeling Bos',
      residence: 'Residentie Reynaert',
    },
    contents: {
      count: 2,
      description: 'products',
    },
    date: 'Jan 7, 2025',
    status: 'Completed',
  },
]
