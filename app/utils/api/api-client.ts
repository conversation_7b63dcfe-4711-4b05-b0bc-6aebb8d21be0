import { AuthService } from '~/api/auth/services/auth.service'
import { userKeys } from '~/api/user'
import { queryClient } from '~/plugins/vue-query'
import { getApiBaseUrl } from './api-base-url.constant'

export function useApiClient<T>() {
  const userStore = useUserStore()

  return $fetch.create<T>({
    baseURL: getApiBaseUrl(),
    onRequest({ options }) {
      if (userStore.accessToken) {
        options.headers.set('Authorization', `Bearer ${userStore.accessToken}`)
      }
    },
    onResponseError: async ({ response, request, options }) => {
      if (response.status !== 401 || !userStore.refreshToken) {
        throw createError({
          statusCode: response.status,
          statusMessage: (response.statusText || 'unknown').toLowerCase(),
        })
      }

      try {
        const tokenResponse = await AuthService.refreshToken(userStore.refreshToken)

        userStore.setAuth(tokenResponse)

        const retryOptions = {
          ...options,
          headers: {
            ...options.headers,
            Authorization: `Bearer ${tokenResponse.access_token}`,
          },
          method: options.method as 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD' | undefined,
        }

        return await $fetch(request, retryOptions)
      }
      catch {
        // TODO: Throw error to Sentry
        queryClient.removeQueries({
          queryKey: userKeys.me.queryKey,
        })
        userStore.clearAuth()
        navigateTo('/login')
      }
    },
  })
}
