import { mockNuxtImport } from '@nuxt/test-utils/runtime'
import { createTestingPinia } from '@pinia/testing'
import { http, HttpResponse } from 'msw'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { mockServer } from '~~/test/test.setup'
import { AuthService } from '~/api/auth/services/auth.service'
import { buildTokenResponseSchema } from '~/schemas/auth/token-response.schema.builder'
import { getApiBaseUrl } from './api-base-url.constant'
import { useApiClient } from './api-client'

vi.mock('~/api/auth/services/auth.service', () => ({
  AuthService: {
    refreshToken: vi.fn(),
  },
}))

let testEndpoint: string

describe('useApiFetch - token refresh', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    createTestingPinia({
      stubActions: false,
    })

    testEndpoint = `${getApiBaseUrl()}/test/endpoint`
  })

  it('retries the request after successful token refresh', async () => {
    // Arrange
    const store = useUserStore()
    store.refreshToken = 'valid-refresh-token'
    store.setAuth = vi.fn()

    const refreshTokenResponse = buildTokenResponseSchema()
    const mockedAuthService = vi.mocked(AuthService, true)
    mockedAuthService.refreshToken.mockResolvedValue(refreshTokenResponse)

    const requestLog: string[] = []

    mockServer.use(
      http.get(testEndpoint, ({ request }) => {
        const auth = request.headers.get('Authorization')
        requestLog.push(auth || '')

        return auth === `Bearer ${refreshTokenResponse.access_token}`
          ? HttpResponse.json({ data: {} }, { status: 200 })
          : HttpResponse.json({ message: 'unauthorized' }, { status: 401 })
      }),
    )

    // Act
    const api = useApiClient()
    await expect(api(testEndpoint, { method: 'GET' })).rejects.toThrowError()

    // Assert
    expect(AuthService.refreshToken).toHaveBeenCalledWith('valid-refresh-token')
    expect(store.setAuth).toHaveBeenCalledWith(refreshTokenResponse)
    expect(requestLog).toHaveLength(2)
  })

  it('logs out and redirects on refresh failure', async () => {
    // Arrange
    mockServer.use(
      http.get(testEndpoint, () => {
        return HttpResponse.json({ message: 'unauthorized' }, { status: 401 })
      }),
    )

    const mockedAuthService = vi.mocked(AuthService, true)
    mockedAuthService.refreshToken.mockRejectedValue(
      new Error('Token refresh failed') as unknown,
    )

    const store = useUserStore()
    store.refreshToken = 'invalid-refresh-token'
    store.logout = vi.fn()

    const { navigateTo } = vi.hoisted(() => ({
      navigateTo: vi.fn(),
    }))
    mockNuxtImport('navigateTo', () => navigateTo)

    // Act
    const api = useApiClient()
    await expect(api(testEndpoint, { method: 'GET' })).rejects.toThrowError()

    // Assert
    expect(store.logout).toHaveBeenCalled()
    expect(navigateTo).toHaveBeenCalledWith('/login')
  })

  it('throws if response is not 401', async () => {
    // Arrange
    mockServer.use(
      http.get(testEndpoint, () => {
        return HttpResponse.json({ message: 'forbidden' }, { status: 403 })
      }),
    )

    const store = useUserStore()
    store.accessToken = 'invalid-token'

    // Act
    // Assert
    const api = useApiClient()
    await expect(api(testEndpoint, { method: 'GET' })).rejects.toThrow('forbidden')
  })
})
