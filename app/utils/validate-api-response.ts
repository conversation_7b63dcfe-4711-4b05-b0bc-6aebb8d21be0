import type { ZodSchema } from 'zod'

export class ApiResponseValidationError extends Error {
  constructor(public readonly issues: any) {
    super('Invalid API response format')
    this.name = 'ResponseValidationError'
  }
}

export function validateApiResponse<T>(
  schema: ZodSchema<T>,
  data: unknown,
): T {
  const result = schema.safeParse(data)

  if (!result.success) {
    const formattedIssues = result.error.format()

    console.error('[Zod] Response validation failed:', formattedIssues)
    throw new ApiResponseValidationError(formattedIssues)
  }

  return result.data
}
