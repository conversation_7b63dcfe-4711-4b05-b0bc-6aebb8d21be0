import type { TokenResponse } from '~/schemas/auth/token-response.schema'
import { COOKIE_NAME } from '~/utils/cookie/cookie-names.enum'

export const useUserStore = defineStore('user', () => {
  const accessToken = useCookie<string | null>(
    COOKIE_NAME.ACCESS_TOKEN,
    {
      sameSite: 'strict',
      secure: true,
    },
  )
  const refreshToken = useCookie<string | null>(
    COOKIE_NAME.REFRESH_TOKEN,
    {
      sameSite: 'strict',
      secure: true,
    },
  )

  const setAuth = (data: TokenResponse) => {
    accessToken.value = data.access_token
    refreshToken.value = data.refresh_token
  }

  const clearAuth = () => {
    accessToken.value = null
    refreshToken.value = null
  }

  const isLoggedIn = computed(() => !!accessToken.value)

  return { accessToken, refreshToken, setAuth, clearAuth, isLoggedIn }
})
