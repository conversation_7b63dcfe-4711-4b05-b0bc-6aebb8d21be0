import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import TestButton from './TestButton.vue'

describe('TestButton', () => {
  it('mounts successfully and renders default label', () => {
    // Arrange & Act
    const wrapper = mount(TestButton)
    
    // Assert
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.text()).toBe('Click me')
    expect(wrapper.find('button').exists()).toBe(true)
  })

  it('renders custom label when provided', () => {
    // Arrange & Act
    const wrapper = mount(TestButton, {
      props: {
        label: 'Custom Button'
      }
    })
    
    // Assert
    expect(wrapper.text()).toBe('Custom Button')
  })

  it('applies primary variant classes by default', () => {
    // Arrange & Act
    const wrapper = mount(TestButton)
    
    // Assert
    const button = wrapper.find('button')
    expect(button.classes()).toContain('bg-blue-500')
    expect(button.classes()).toContain('text-white')
  })

  it('applies secondary variant classes when specified', () => {
    // Arrange & Act
    const wrapper = mount(TestButton, {
      props: {
        variant: 'secondary'
      }
    })
    
    // Assert
    const button = wrapper.find('button')
    expect(button.classes()).toContain('bg-gray-200')
    expect(button.classes()).toContain('text-gray-800')
  })

  it('emits click event when button is clicked', async () => {
    // Arrange
    const wrapper = mount(TestButton)
    
    // Act
    await wrapper.find('button').trigger('click')
    
    // Assert
    expect(wrapper.emitted('click')).toBeTruthy()
    expect(wrapper.emitted('click')).toHaveLength(1)
  })
})
