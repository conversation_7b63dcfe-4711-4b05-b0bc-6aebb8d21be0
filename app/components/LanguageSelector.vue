<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { locale, setLocale } = useI18n()

const items = computed(() => [
  [{
    label: 'English',
    icon: 'i-twemoji-flag-united-states',
    disabled: locale.value === 'en',
    onSelect: () => setLocale('en'),
  }],
  [{
    label: 'Nederlands',
    icon: 'i-twemoji-flag-netherlands',
    disabled: locale.value === 'nl',
    onSelect: () => setLocale('nl'),
  }],
])

const currentLocaleIcon = computed(() => {
  return locale.value === 'en' ? 'i-twemoji-flag-united-states' : 'i-twemoji-flag-netherlands'
})
</script>

<template>
  <UDropdownMenu :items="items">
    <UButton :icon="currentLocaleIcon" color="neutral" variant="outline" />
  </UDropdownMenu>
</template>
