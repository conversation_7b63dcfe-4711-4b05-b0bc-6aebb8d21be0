<script setup lang="ts">
import type { PropType } from 'vue'
import type { Order } from '~/types/order/order.type'

defineProps({
  order: {
    type: Object as PropType<Order>,
    required: true,
  },
})
</script>

<template>
  <tr class="hover:bg-gray-50">
    <td class="pr-1 pl-9 py-2 align-top whitespace-nowrap text-sm font-medium text-gray-900 text-center text-gray-500 sm:text-left">
      <span class="inline-block min-w-[60px] text-gray-500">{{ order.id }}</span>
    </td>
    <td class="pl-1 pr-4 py-2 align-top whitespace-nowrap">
      <div class="text-sm font-medium text-gray-900">
        {{ order.client.name }}
      </div>
      <div class="text-sm text-gray-500">
        {{ order.client.department }}
      </div>
      <div class="text-sm text-gray-500">
        {{ order.client.residence }}
      </div>
    </td>
    <td class="px-6 py-2 align-top whitespace-nowrap text-sm text-gray-500">
      {{ order.contents.count }} {{ order.contents.description }}
    </td>
    <td class="px-6 py-2 align-top whitespace-nowrap text-sm text-gray-500">
      {{ order.date }}
    </td>
    <td class="px-6 py-2 align-top whitespace-nowrap">
      <UBadge
        :color="order.status === 'Submitted' ? 'info' : 'success'"
        variant="subtle"
        size="sm"
      >
        {{ order.status }}
      </UBadge>
    </td>
    <td class="px-6 py-2 align-top whitespace-nowrap text-right text-sm font-medium">
      <UButton color="success" variant="solid" size="sm" class="text-white">
        Reorder
      </UButton>
    </td>
  </tr>
</template>
