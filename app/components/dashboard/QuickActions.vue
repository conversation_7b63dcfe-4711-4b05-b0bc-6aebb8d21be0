<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import QuickActionsItem from './QuickActionsItem.vue'

const { t } = useI18n()

const items = computed(() => [
  {
    title: t('quick_actions.place_order.title'),
    description: t('quick_actions.place_order.description'),
    icon: 'i-lucide-file-pen',
  },
  {
    title: t('quick_actions.orders.title'),
    description: t('quick_actions.orders.description'),
    icon: 'i-lucide-file-text',
  },
  {
    title: t('quick_actions.addresses.title'),
    description: t('quick_actions.addresses.description'),
    icon: 'i-lucide-map-pin-house',
  },
  {
    title: t('quick_actions.products.title'),
    description: t('quick_actions.products.description'),
    icon: 'i-lucide-pill-bottle',
  },
  {
    title: t('quick_actions.support.title'),
    description: t('quick_actions.support.description'),
    icon: 'i-lucide-circle-help',
  },
])

const gridClass = computed(() => {
  const cols = ['md:grid-cols-1', 'md:grid-cols-2', 'md:grid-cols-3', 'md:grid-cols-4', 'md:grid-cols-5']
  return cols[Math.min(items.value.length, 5) - 1] || 'md:grid-cols-5'
})
</script>

<template>
  <div class="bg-green-50 p-4 mb-8 mx-0 sm:mx-2 md:mx-4 lg:mx-6 xl:mx-8 mt-0 sm:rounded-b-lg md:rounded-b-xl lg:rounded-b-2xl rounded-none">
    <h1 class="text-lg font-medium text-gray-700 mb-4">
      {{ t('quick_actions.title') }}
    </h1>
    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden mb-8">
      <div class="grid grid-cols-1 divide-y md:divide-y-0 md:divide-x divide-gray-200" :class="gridClass">
        <QuickActionsItem
          v-for="(item, index) in items"
          :key="index"
          :title="item.title"
          :description="item.description"
          :icon="item.icon"
        />
      </div>
    </div>
  </div>
</template>
