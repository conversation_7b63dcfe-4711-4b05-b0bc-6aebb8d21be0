<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>

<template>
  <UCard class="p-12">
    <div class="flex flex-col items-center justify-center text-center">
      <UIcon name="i-lucide-inbox" size="50" class="text-gray-400 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        {{ t('recent_orders.no_orders.title') }}
      </h3>
      <p class="text-gray-500 mb-6">
        {{ t('recent_orders.no_orders.description') }}
      </p>
      <UButton
        to="/order/quick"
        color="primary"
        class="mx-auto"
      >
        {{ t('recent_orders.no_orders.button') }}
      </UButton>
    </div>
  </UCard>
</template>
