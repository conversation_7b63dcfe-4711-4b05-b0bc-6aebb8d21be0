<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const defaultClasses = 'px-6 py-2 text-left text-sm font-bold tracking-wider'

const headers = computed(() => [
  {
    label: t('orders_table.headers.order_id'),
    align: 'center',
    hasSort: true,
    classes: 'pr-1 pl-6 py-2 text-left text-sm font-bold tracking-wider text-center',
  },
  {
    label: t('orders_table.headers.client'),
    align: 'left',
    hasSort: false,
    classes: 'pl-1 pr-4 py-2 text-left text-sm font-bold tracking-wider',
  },
  { label: t('orders_table.headers.contents'), align: 'left', hasSort: false },
  { label: t('orders_table.headers.date'), align: 'left', hasSort: false },
  { label: t('orders_table.headers.status'), align: 'left', hasSort: false },
  { label: '', align: 'right', hasSort: false, classes: `${defaultClasses} text-right` },
])
</script>

<template>
  <thead class="bg-gray-50">
    <tr>
      <th v-for="(header, index) in headers" :key="index" scope="col" :class="header.classes || defaultClasses">
        <div v-if="header.label" class="flex items-center whitespace-nowrap" :class="header.align === 'center' ? 'justify-center sm:justify-start' : ''">
          {{ header.label }}
          <UIcon v-if="header.hasSort" name="i-lucide-chevron-down" class="ml-1 h-4 w-4" />
        </div>
      </th>
    </tr>
  </thead>
</template>
