<script setup lang="ts">
interface Props {
  title: string
  description: string
  icon: string
}

defineProps<Props>()
</script>

<template>
  <div class="flex items-center p-4 hover:bg-gray-50 transition-colors cursor-pointer md:flex-col md:items-start">
    <div class="text-green-500 mr-3 md:mr-0 md:mb-2">
      <UIcon :name="icon" class="text-2xl md:text-3xl" />
    </div>
    <div>
      <h3 class="font-medium text-gray-900 text-sm">
        {{ title }}
      </h3>
      <p class="text-xs text-gray-500">
        {{ description }}
      </p>
    </div>
  </div>
</template>
