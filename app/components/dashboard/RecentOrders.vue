<script setup lang="ts">
import type { PropType } from 'vue'
import type { Order } from '~/types/order/order.type'
import { useI18n } from 'vue-i18n'
import OrdersEmptyState from './OrdersEmptyState.vue'
import OrdersTableHeader from './OrdersTableHeader.vue'
import OrdersTableRow from './OrdersTableRow.vue'

defineProps({
  orders: {
    type: Array as PropType<Order[]>,
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const { t } = useI18n()
</script>

<template>
  <div class="px-4">
    <h2 class="text-lg font-medium text-gray-700 mb-4">
      {{ t('recent_orders.title') }}
    </h2>
    <UCard v-if="isLoading" class="p-12">
      <div class="flex justify-center">
        <UIcon name="i-lucide-loader-2" class="animate-spin" size="40" />
      </div>
    </UCard>
    <OrdersEmptyState v-else-if="orders.length === 0" />
    <div v-else class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <OrdersTableHeader />
        <tbody class="bg-white divide-y divide-gray-200">
          <OrdersTableRow
            v-for="order in orders"
            :key="order.id + order.date"
            :order="order"
          />
        </tbody>
      </table>
    </div>
  </div>
</template>
