<script setup lang="ts">
import { refDebounced } from '@vueuse/core'
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

interface FilterOption {
  label: string
  value: string
}
interface Filters {
  medicine: string | null
  brand: string | null
  description: string | null
  cnk: string | null
}

const emits = defineEmits<{
  (e: 'search', query: string): void
  (e: 'filter', filters: Filters): void
}>()

const { t } = useI18n()

const search = ref('')
const selectedMedicine = ref<string | null>(null)
const selectedBrand = ref<string | null>(null)
const selectedDescription = ref<string | null>(null)
const selectedCnk = ref<string | null>(null)

// Debounced search - waits 300ms after user stops typing
const debouncedSearch = refDebounced(search, 300)

// Debounced filters - waits 400ms after user stops changing filters
const filters = computed(() => ({
  medicine: selectedMedicine.value,
  brand: selectedBrand.value,
  description: selectedDescription.value,
  cnk: selectedCnk.value,
}))

const debouncedFilters = refDebounced(filters, 400)

// Mock options for dropdowns with translations
const medicineOptions = computed<FilterOption[]>(() => [
  { label: t('filters.medicine_types.pain_killers'), value: 'pain-killers' },
  { label: t('filters.medicine_types.antibiotics'), value: 'antibiotics' },
  { label: t('filters.medicine_types.anti_inflammatory'), value: 'anti-inflammatory' },
])

const brandOptions = computed<FilterOption[]>(() => [
  { label: t('filters.brands.afebryl'), value: 'afebryl' },
  { label: t('filters.brands.aleve'), value: 'aleve' },
  { label: t('filters.brands.algocod'), value: 'algocod' },
  { label: t('filters.brands.algostase'), value: 'algostase' },
])

const descriptionOptions = computed<FilterOption[]>(() => [
  { label: t('filters.descriptions.tablets'), value: 'tablets' },
  { label: t('filters.descriptions.effervescent'), value: 'effervescent' },
  { label: t('filters.descriptions.syrup'), value: 'syrup' },
])

const cnkOptions: FilterOption[] = [
  { label: '324095', value: '324095' },
  { label: '987654', value: '987654' },
  { label: '456789', value: '456789' },
]

// Watch debounced search
watch(debouncedSearch, (newSearch) => {
  emits('search', newSearch)
})

// Watch debounced filters
watch(debouncedFilters, (newFilters) => {
  emits('filter', newFilters)
}, { deep: true })
</script>

<template>
  <div class="border-b border-gray-200 pb-4 mb-6">
    <div class="flex flex-col sm:flex-row flex-wrap gap-2">
      <div class="grid grid-cols-2 sm:flex gap-2 w-full sm:w-auto">
        <USelectMenu
          v-model="selectedMedicine"
          :options="medicineOptions"
          :placeholder="t('filters.placeholders.medicine')"
          class="w-full sm:w-40"
        />

        <USelectMenu
          v-model="selectedBrand"
          :options="brandOptions"
          :placeholder="t('filters.placeholders.brand')"
          class="w-full sm:w-40"
        />

        <USelectMenu
          v-model="selectedDescription"
          :options="descriptionOptions"
          :placeholder="t('filters.placeholders.description')"
          class="w-full sm:w-40"
        />

        <USelectMenu
          v-model="selectedCnk"
          :options="cnkOptions"
          :placeholder="t('filters.placeholders.cnk_number')"
          class="w-full sm:w-40"
        />
      </div>

      <UButton
        variant="soft"
        color="neutral"
        class="mt-2 sm:mt-0 sm:ml-2"
      >
        {{ t('filters.more_filters') }}
      </UButton>

      <div class="w-full sm:w-auto sm:ml-auto mt-4 sm:mt-0">
        <UInput
          v-model="search"
          icon="i-heroicons-magnifying-glass-20-solid"
          :placeholder="t('filters.placeholders.search')"
          class="w-full sm:w-64"
        />
      </div>
    </div>
  </div>
</template>
