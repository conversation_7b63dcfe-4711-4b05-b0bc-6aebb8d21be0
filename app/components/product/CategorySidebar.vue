<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { categories } from '~/utils/mockProductsData'

defineProps({
  selectedCategory: {
    type: String,
    default: 'all',
  },
  showRecentlyOrdered: {
    type: Boolean,
    default: true,
  },
})

defineEmits<{
  (e: 'selectCategory', category: string): void
}>()

const { t } = useI18n()
</script>

<template>
  <div class="border-r border-gray-200 pr-4">
    <h2 class="text-lg font-semibold mb-4">
      {{ selectedCategory === 'recently-ordered' ? t('categories.recently_ordered') : t('categories.title') }}
    </h2>

    <div
      v-if="showRecentlyOrdered"
      class="relative mb-2"
      :class="selectedCategory === 'recently-ordered' ? 'border-l-4 border-green-500' : ''"
    >
      <UButton
        block
        variant="ghost"
        :color="selectedCategory === 'recently-ordered' ? 'primary' : 'neutral'"
        class="justify-start text-left"
        @click="$emit('selectCategory', 'recently-ordered')"
      >
        {{ t('categories.recently_ordered') }}
      </UButton>
    </div>

    <div class="space-y-1 overflow-y-auto pb-2">
      <div
        v-for="category in categories"
        :key="category.id"
        class="relative mb-1"
        :class="selectedCategory === category.slug ? 'border-l-4 border-green-500' : ''"
      >
        <UButton
          block
          variant="ghost"
          :color="selectedCategory === category.slug ? 'primary' : 'neutral'"
          class="justify-start text-left text-sm md:text-base"
          @click="$emit('selectCategory', category.slug)"
        >
          {{ category.name }}
        </UButton>
      </div>
    </div>
  </div>
</template>
