<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  page: {
    type: Number,
    required: true,
  },
  totalItems: {
    type: Number,
    required: true,
  },
  itemsPerPage: {
    type: Number,
    default: 12,
  },
})

const emit = defineEmits(['update:page'])

const { t } = useI18n()

// Total pages computed property
const totalPages = computed(() => {
  return Math.ceil(props.totalItems / props.itemsPerPage)
})

// Return a route object for each page - this makes pagination SEO-friendly
function toPage(page: number) {
  const route = useRoute()
  return {
    query: {
      ...route.query,
      page: page.toString(),
    },
  }
}

function onPageChange(newPage: number) {
  emit('update:page', newPage)
}
</script>

<template>
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-6 gap-4">
    <div class="text-sm text-gray-500 text-center sm:text-left">
      {{ t('pagination.showing') }} {{ Math.min(page * itemsPerPage, totalItems) }}
      {{ t('pagination.of') }} {{ totalItems }} {{ t('pagination.items') }}
    </div>

    <!-- UPagination with links via :to prop -->
    <UPagination
      :page="page"
      :total="totalItems"
      :page-count="totalPages"
      :page-size="itemsPerPage"
      class="justify-center"
      :to="toPage"
      @update:page="onPageChange"
    />
  </div>
</template>
