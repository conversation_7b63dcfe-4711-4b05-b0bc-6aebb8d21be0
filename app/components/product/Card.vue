<script setup lang="ts">
import { useI18n } from 'vue-i18n'

interface Product {
  id: number
  name: string
  cnk: string
  image: string
  category: string
  recently_ordered: boolean
}

const props = defineProps({
  product: {
    type: Object as () => Product,
    required: true,
  },
})

const { t } = useI18n()

function addToCart() {
  // Logic
  console.warn(`Added ${props.product.name} to cart`)
}
</script>

<template>
  <UCard class="h-100 py-3 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 bg-white">
    <div class="flex flex-col h-full">
      <div class="flex-1 flex flex-col">
        <div class="flex justify-center mb-8">
          <img :src="product.image" :alt="product.name" class="w-full h-40 object-contain">
        </div>
        <div class="flex-1 flex flex-col justify-end">
          <h3 class="text-lg font-semibold mb-2 line-clamp-2 text-gray-900">
            {{ product.name }}
          </h3>
          <p class="text-sm text-gray-500 mb-6">
            {{ t('card.cnk_label') }} {{ product.cnk }}
          </p>
        </div>
      </div>

      <UButton
        color="primary"
        variant="soft"
        block
        class="text-sm bg-green-50 hover:bg-green-100 text-green-700 border-none rounded-lg py-3 font-medium mt-auto w-full"
        @click="addToCart"
      >
        {{ t('card.add_to_cart') }}
      </UButton>
    </div>
  </UCard>
</template>
