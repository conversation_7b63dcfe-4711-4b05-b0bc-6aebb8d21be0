<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useLogoutFlow } from '~/composables/auth/useLogoutFlow'

const { logout } = useLogoutFlow()
const route = useRoute()
const { t } = useI18n()

const items = computed(() => {
  return [
    { label: t('navigation.dashboard'), to: '/', active: route.path === '/' || route.path === '/dashboard' },
    { label: t('navigation.orders'), to: '/orders', active: route.path === '/orders' || route.path.startsWith('/orders/') },
    { label: t('navigation.addresses'), to: '/addresses', active: route.path === '/addresses' || route.path.startsWith('/addresses/') },
    { label: t('navigation.products'), to: '/products', active: route.path === '/products' || route.path.startsWith('/products/') },
    { label: t('navigation.support'), to: '/support', active: route.path === '/support' || route.path.startsWith('/support/') },
  ]
})

const userItems = computed(() => [
  [{
    label: t('navigation.my_account'),
    icon: 'i-lucide-user',
    to: '/settings',
  }],
  [{
    label: t('navigation.log_out'),
    icon: 'i-ph-sign-out',
    onSelect: () => {
      logout()
    },
  }],
])
</script>

<template>
  <UHeader class="bg-white shadow-sm">
    <template #title>
      <img class="h-10" src="~/assets/img/farmapunt–logo.png" alt="Hyperfox logo">
    </template>

    <!-- Desktop Navigation -->
    <UNavigationMenu
      :items="items"
      orientation="horizontal"
      class="hidden lg:flex"
    />

    <template #right>
      <LanguageSelector />
      <UButton
        to="/order/quick"
        :label="$t('navigation.place_quick_order')"
        variant="outline"
        color="neutral"
        class="mr-2 bg-white text-black hidden sm:inline-flex"
      />
      <div class="relative">
        <UButton icon="i-lucide-shopping-cart" to="/" variant="ghost" color="neutral" size="lg" />
        <UBadge color="primary" size="sm" class="absolute -top-1 -right-1">
          5
        </UBadge>
      </div>
      <UDropdownMenu :items="userItems">
        <UButton icon="i-lucide-user-circle" variant="ghost" color="neutral" />
      </UDropdownMenu>
    </template>

    <!-- Personalized mobile menu -->
    <template #body>
      <div class="flex flex-col h-[calc(100vh-80px)]">
        <!-- Main navigation  -->
        <div class="flex-grow flex flex-col items-center justify-center pt-16 pb-8 space-y-10">
          <NuxtLink
            v-for="item in items"
            :key="item.label"
            :to="item.to"
            class="text-xl text-green-500 font-medium"
            :class="{ 'text-green-600 font-semibold': item.active }"
          >
            {{ item.label }}
            <UIcon v-if="item.label === t('navigation.support')" name="i-lucide-arrow-up-right" class="inline-block ml-1" />
          </NuxtLink>

          <!-- Place quick order button -->
          <UButton
            to="/order/quick"
            :label="$t('navigation.place_quick_order')"
            variant="outline"
            color="neutral"
            class="mt-8 px-8 py-2 border border-gray-300 rounded-full text-gray-700"
          />
        </div>

        <!-- Account section -->
        <div class="border-t border-gray-200 py-6 mt-auto">
          <div class="flex justify-center pb-4">
            <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
              <UIcon name="i-lucide-Header" class="text-gray-500 text-xl" />
            </div>
          </div>

          <div class="flex justify-center space-x-6">
            <div class="text-gray-700">
              {{ $t('navigation.my_account') }}
            </div>
            <div class="text-gray-700">
              {{ $t('navigation.settings') }}
            </div>
            <div class="text-gray-700 cursor-pointer" @click="logout">
              {{ $t('navigation.log_out') }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </UHeader>
</template>
