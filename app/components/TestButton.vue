<script setup lang="ts">
interface Props {
  label?: string
  variant?: 'primary' | 'secondary'
}

const props = withDefaults(defineProps<Props>(), {
  label: 'Click me',
  variant: 'primary'
})

const emit = defineEmits<{
  click: []
}>()

function handleClick() {
  emit('click')
}
</script>

<template>
  <button 
    :class="[
      'px-4 py-2 rounded font-medium transition-colors',
      variant === 'primary' 
        ? 'bg-blue-500 hover:bg-blue-600 text-white' 
        : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
    ]"
    @click="handleClick"
  >
    {{ label }}
  </button>
</template>
