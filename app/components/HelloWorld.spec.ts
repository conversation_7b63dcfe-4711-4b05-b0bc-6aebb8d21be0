import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import HelloWorld from './HelloWorld.vue'

describe('HelloWorld - Smoke Test', () => {
  it('mounts component and asserts HTML content', () => {
    // Arrange & Act - Mount the component
    const wrapper = mount(HelloWorld)
    
    // Assert - Check that component exists and has expected HTML
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('h1').text()).toBe('Hello World!')
    expect(wrapper.find('p').text()).toBe('This is a simple component for testing.')
    expect(wrapper.find('.hello-world').exists()).toBe(true)
  })

  it('renders custom message when prop is provided', () => {
    // Arrange & Act
    const customMessage = 'Custom Test Message'
    const wrapper = mount(HelloWorld, {
      props: {
        msg: customMessage
      }
    })
    
    // Assert
    expect(wrapper.find('h1').text()).toBe(customMessage)
  })
})
