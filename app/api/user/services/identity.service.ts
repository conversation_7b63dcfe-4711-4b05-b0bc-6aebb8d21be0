import type { UserInfoResponse } from '~/schemas/identity/user-info-response.schema'
import type { ApiResponse } from '~/types/api/api-response'
import { userInfoResponseSchema } from '~/schemas/identity/user-info-response.schema'
import { useApiClient } from '~/utils/api/api-client'
import { IDENTITY_SERVICE_API_ROUTES } from './identity-service.routes'

export class IdentityService {
  static async getUserInfo(): Promise<UserInfoResponse> {
    const raw = await useApiClient<ApiResponse<UserInfoResponse>>()(
      IDENTITY_SERVICE_API_ROUTES.ME,
    )

    return validateApiResponse(userInfoResponseSchema, raw.data)
  }

  static async logout(): Promise<void> {
    await useApiClient<void>()(IDENTITY_SERVICE_API_ROUTES.LOGOUT, {
      method: 'POST',
    })
  }
}
