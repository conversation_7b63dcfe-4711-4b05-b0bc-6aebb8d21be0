import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { userKeys } from '../keys/user.keys'
import { IdentityService } from '../services/identity.service'

export function useLogout() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => IdentityService.logout(),
    onSuccess: () => {
      queryClient.removeQueries({
        queryKey: userKeys.me.queryKey,
      })
    },
  })
}
