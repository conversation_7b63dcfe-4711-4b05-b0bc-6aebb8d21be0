import type { TokenResponse } from '~/schemas/auth/token-response.schema'
import { tokenResponseSchema } from '~/schemas/auth/token-response.schema'
import { getApiBaseUrl } from '~/utils/api/api-base-url.constant'
import { getOauthRedirectUrl } from '~/utils/auth/auth-redirect.constant'
import { getOauthClientId } from '~/utils/auth/client-id.constant'
import { validateApiResponse } from '~/utils/validate-api-response'
import { AUTH_SERVICE_API_ROUTES } from './auth-service.routes'

let isRefreshing = false
let refreshPromise: Promise<TokenResponse> | null = null

export class AuthService {
  static async getToken(code: string, verifier: string): Promise<TokenResponse> {
    const result = await $fetch(AUTH_SERVICE_API_ROUTES.TOKEN, {
      method: 'POST',
      baseURL: getApiBaseUrl(),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: getOauthRedirectUrl(),
        client_id: getOauthClientId(),
        code_verifier: verifier,
      }).toString(),
    })

    return validateApiResponse(tokenResponseSchema, result)
  }

  static async refreshToken(refreshToken: string): Promise<TokenResponse> {
    if (isRefreshing) {
      return refreshPromise!
    }

    isRefreshing = true
    refreshPromise = $fetch(AUTH_SERVICE_API_ROUTES.TOKEN, {
      method: 'POST',
      baseURL: getApiBaseUrl(),
      body: {
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: getOauthClientId(),
      },
    })

    try {
      const result = await refreshPromise

      return validateApiResponse(tokenResponseSchema, result)
    }
    finally {
      isRefreshing = false
      refreshPromise = null
    }
  }
}
