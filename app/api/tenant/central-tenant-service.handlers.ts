import { http, HttpResponse } from 'msw'
import { buildTenantResponseSchema } from '~/schemas/tenant/central-tenant-response.schema.builders'
import { CENTAL_TENANT_SERVICE_API_ROUTES } from './central-tenant-service.routes'

export const centralTenantServiceHandlers = [
  http.get(CENTAL_TENANT_SERVICE_API_ROUTES.TENANT, () => {
    return HttpResponse.json(buildTenantResponseSchema(), { status: 200 })
  }),
]
