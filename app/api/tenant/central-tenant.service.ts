import type { TenantDomain } from '~/schemas/tenant/central-tenant-response.schema'
import { tenantResponseSchema } from '~/schemas/tenant/central-tenant-response.schema'
import { CENTAL_TENANT_SERVICE_API_ROUTES } from './central-tenant-service.routes'

export class CentralTenantService {
  public static async getTenantDomains(): Promise<TenantDomain[]> {
    const result = await $fetch(CENTAL_TENANT_SERVICE_API_ROUTES.TENANT, {
      method: 'GET',
    })

    const tenentResponse = validateApiResponse(tenantResponseSchema, result)

    return tenentResponse.data.domains
  }
}
