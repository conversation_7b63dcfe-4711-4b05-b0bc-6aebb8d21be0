export class ApiResponse<T> {
  constructor(public data: T) {
  }
}

export class PaginatedApiResponse<T> extends ApiResponse<T> {
  constructor(
    public override data: T,
    public meta: Meta,
  ) {
    super(data)
  }
}

export interface MetaLink {
  url: string | null
  label: string
  active: boolean
}

export interface Meta {
  current_page: number
  from: number
  last_page: number
  links: MetaLink[]
  path: string
  per_page: number
  to: number
  total: number
}
