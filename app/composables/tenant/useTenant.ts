import { CentralTenantService } from '~/api/tenant/central-tenant.service'
import { getCurrentOrigin } from '~/utils/client/getOrigin'

export function useTenant() {
  const tenantDomain = useState<string | null | undefined>('tenant')
  const router = useRouter()
  const currentlyFetchedTenant = useState<string | null | undefined>('currentlyFetchedTenant')

  const getHostname = () => {
    const host = getCurrentOrigin()
    if (!host) {
      throw new Error('Unable to determine hostname')
    }

    return host
  }

  const getTenantDomain = async (host: string): Promise<string> => {
    const domains = await CentralTenantService.getTenantDomains()

    const firstDomain = domains[0]?.domain
    if (!firstDomain) {
      router.push('/error/no-tenant')
      throw new Error(`No domains found for tenant ${host}`)
    }

    return firstDomain
  }

  const initializeTenant = async () => {
    const host = getHostname()

    if (currentlyFetchedTenant.value && currentlyFetchedTenant.value === host)
      return

    tenantDomain.value = await getTenantDomain(host)
    currentlyFetchedTenant.value = host
  }

  const safeComputed = <T>(fn: () => T) =>
    computed(() => {
      if (!tenantDomain.value)
        throw new Error('Tenant not initialized')
      return fn()
    })

  const apiUrl = safeComputed(() => `https://${tenantDomain.value}/`)

  const isTenantInitialized = computed(() => !!tenantDomain.value)

  return {
    apiUrl,
    isTenantInitialized,
    initializeTenant,
  }
}
