import type { TokenResponse } from '~/schemas/auth/token-response.schema'

interface UsePKCE {
  generateCodeVerifier: () => string
  generateCodeChallenge: (verifier: string) => Promise<string>
}

interface UseAuthOptions {
  getRedirectUri: () => string
  getClientId: () => string
  getApiBaseUrl: () => string
  onTokenReceived: (token: TokenResponse) => void
  fetchToken: (code: string, verifier: string) => Promise<TokenResponse>
  getQueryCode?: () => string | undefined
  usePKCE: () => UsePKCE
}

export function useAuth({
  getRedirectUri,
  getClientId,
  getApiBaseUrl,
  onTokenReceived,
  fetchToken,
  usePKCE,
}: UseAuthOptions) {
  const { generateCodeVerifier, generateCodeChallenge } = usePKCE()

  const generateRedirectUrl = (challenge: string) => {
    const url = new URL(`${getApiBaseUrl()}oauth/authorize`)
    url.searchParams.set('response_type', 'code')
    url.searchParams.set('client_id', getClientId())
    url.searchParams.set('redirect_uri', getRedirectUri())
    url.searchParams.set('scope', '')
    url.searchParams.set('state', '')
    url.searchParams.set('code_challenge', challenge)
    url.searchParams.set('code_challenge_method', 'S256')
    return url.toString()
  }

  const login = async () => {
    const verifier = generateCodeVerifier()
    const challenge = await generateCodeChallenge(verifier)
    sessionStorage.setItem('pkce_verifier', verifier)

    window.location.href = generateRedirectUrl(challenge)
  }

  const handleRedirectCallback = async () => {
    const { query } = useRoute()
    const code = query.code
    if (!code || typeof code !== 'string')
      throw new Error('Missing authorization code')

    const verifier = sessionStorage.getItem('pkce_verifier')
    if (!verifier)
      throw new Error('Missing PKCE verifier')

    const token = await fetchToken(code, verifier)
    onTokenReceived(token)
  }

  return { login, handleRedirectCallback, generateRedirectUrl }
}
