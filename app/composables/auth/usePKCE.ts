export function usePKCE(encodeBase64: (input: string) => Promise<string>) {
  const generateCodeVerifier = (): string => {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~'
    const array = new Uint8Array(64)
    crypto.getRandomValues(array)
    return Array.from(array).map(x => charset[x % charset.length]).join('')
  }

  const generateCodeChallenge = async (verifier: string): Promise<string> => {
    return await encodeBase64(verifier)
  }

  return { generateCodeVerifier, generateCodeChallenge }
}
