import { describe, expect, it } from 'vitest'
import { useMyPKCE } from './useMyPKCE'

function useTestPKCE() {
  return useMyPKCE()
}

describe('usePKCE', () => {
  it('generates a valid code challenge from a verifier', async () => {
    // Act
    const { generateCodeChallenge } = useTestPKCE()
    const challenge = await generateCodeChallenge('test-verifier-123')

    // Assert
    expect(typeof challenge).toBe('string')
    expect(challenge.length).toBeGreaterThanOrEqual(43)
    expect(challenge).not.toContain('+')
    expect(challenge).not.toContain('/')
    expect(challenge).not.toContain('=')
  })

  it('produces deterministic challenge for same verifier', async () => {
    // Act
    const { generateCodeChallenge } = useTestPKCE()

    const challenge1 = await generateCodeChallenge('static-verifier')
    const challenge2 = await generateCodeChallenge('static-verifier')

    // Assert
    expect(challenge1).toBe(challenge2)
  })

  it('generates a challenge that matches expected hash of the verifier', async () => {
    // Arrange
    const { generateCodeChallenge } = useTestPKCE()

    const knownVerifier = 'dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk'
    const expectedChallenge = 'E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM'

    // Act
    const actualChallenge = await generateCodeChallenge(knownVerifier)

    // Assert
    expect(actualChallenge).toBe(expectedChallenge)
  })
})
