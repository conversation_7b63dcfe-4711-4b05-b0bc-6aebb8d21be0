import type { CartItem } from '~/schemas/cart/cart-item.schema'
import { COOKIE_NAME } from '~/utils/cookie/cookie-names.enum'

export function useCart() {
  const cart = useCookie<CartItem[]>(COOKIE_NAME.CART, {
    maxAge: 60 * 60 * 24 * 7,
    default: () => [],
  })

  const getFromCart = (productId: string): CartItem | null => {
    return cart.value.find(item => item.productId === productId) || null
  }

  const addToCart = (item: CartItem) => {
    if (item.quantity <= 0)
      return

    const existingItem = getFromCart(item.productId)

    if (existingItem) {
      existingItem.quantity += item.quantity
    }
    else {
      cart.value.push({ ...item })
    }
  }

  const removeFromCart = (productId: string) => {
    cart.value = cart.value.filter(item => item.productId !== productId)
  }

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId)
      return
    }

    const item = getFromCart(productId)
    if (item) {
      item.quantity = quantity
    }
  }

  const clearCart = () => {
    cart.value = []
  }

  const cartItems = computed(() => cart.value)
  const cartCount = computed(() =>
    cart.value.reduce((total, item) => total + item.quantity, 0),
  )

  return {
    cart,
    cartItems,
    cartCount,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    getFromCart,
  }
}
