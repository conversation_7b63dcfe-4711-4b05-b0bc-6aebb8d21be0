<script setup lang="ts">
import type { StepperItem } from '@nuxt/ui'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { categories, products } from '~/utils/mockProductsData'

definePageMeta({
  layout: 'default',
})

const { t } = useI18n()

// mobile stepper
const currentStep = ref(0)
const stepper = ref(null)

const stepperItems: StepperItem[] = [
  {
    title: t('stepper.select_products.title'),
    description: t('stepper.select_products.description'),
    icon: 'i-lucide-search',
    slot: 'products',
  },
  {
    title: t('stepper.complete_order.title'),
    description: t('stepper.complete_order.description'),
    icon: 'i-lucide-check-circle',
    slot: 'complete',
  },
]

// Reactive state
const searchQuery = ref('')
const billingAddress = ref('')
const deliveryAddress = ref('')
const poNumber = ref('')
const remarks = ref('')

// Quantity selector state
const selectedProduct = ref<any>(null)
const selectedQuantity = ref(1)

// Order products - productos agregados al pedido
const orderProducts = ref<Array<{
  id: number
  name: string
  cnk: string
  image: string
  category: string
  quantity: number
}>>([
  {
    id: 1,
    name: 'Hyalu B5 pure Hyaluronic Acid',
    cnk: '324095',
    image: 'https://pics.walgreens.com/prodimg/10284/900.jpg',
    category: 'dermatology',
    quantity: 2,
  },
])

// Search results from catalog
const searchResults = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < 2) {
    return []
  }

  const query = searchQuery.value.toLowerCase()
  return products.filter(product =>
    product.name.toLowerCase().includes(query)
    || product.cnk.includes(query),
  ).slice(0, 5) // Limitar a 5 resultados
})

// Get category name from slug
function getCategoryName(categorySlug: string) {
  const category = categories.find(cat => cat.slug === categorySlug)
  return category ? category.name : categorySlug
}

// Address options (mock data)
const addressOptions = [
  { label: 'Select billing address', value: '' },
  { label: 'Main Office - 123 Business St', value: 'main-office' },
  { label: 'Branch Office - 456 Commerce Ave', value: 'branch-office' },
]

// Stepper navigation methods
function nextStep() {
  if (currentStep.value < stepperItems.length - 1) {
    currentStep.value++
  }
}

function prevStep() {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

function canProceedToNext() {
  if (currentStep.value === 0) {
    // First step: at least one product should be added
    return orderProducts.value.length > 0
  }
  return true
}

// Methods
function showQuantitySelector(product: any) {
  selectedProduct.value = product
  selectedQuantity.value = 1
}

function addToOrder(product: any, quantity: number = 1) {
  const existingProduct = orderProducts.value.find(p => p.id === product.id)

  if (existingProduct) {
    existingProduct.quantity += quantity
  }
  else {
    orderProducts.value.push({
      id: product.id,
      name: product.name,
      cnk: product.cnk,
      image: product.image,
      category: product.category,
      quantity,
    })
  }

  selectedProduct.value = null
  searchQuery.value = ''
}

function confirmAddToOrder() {
  if (selectedProduct.value) {
    addToOrder(selectedProduct.value, selectedQuantity.value)
  }
}

function cancelQuantitySelector() {
  selectedProduct.value = null
  selectedQuantity.value = 1
}

function increaseSelectedQuantity() {
  selectedQuantity.value++
}

function decreaseSelectedQuantity() {
  if (selectedQuantity.value > 1) {
    selectedQuantity.value--
  }
}

function increaseQuantity(productId: number) {
  const product = orderProducts.value.find(p => p.id === productId)
  if (product) {
    product.quantity++
  }
}

function decreaseQuantity(productId: number) {
  const product = orderProducts.value.find(p => p.id === productId)
  if (product && product.quantity > 0) {
    product.quantity--

    // Si la cantidad llega a 0, remover del pedido
    if (product.quantity === 0) {
      const index = orderProducts.value.findIndex(p => p.id === productId)
      if (index > -1) {
        orderProducts.value.splice(index, 1)
      }
    }
  }
}

function findInCatalog() {
  // Navigate to products page with search
  navigateTo(`/products?search=${searchQuery.value}`)
}

function placeOrder() {
  // Handle order placement
}

function removeAllItems() {
  orderProducts.value.splice(0, orderProducts.value.length)
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="py-8">
      <UContainer>
        <h1 class="text-2xl font-bold text-gray-900 mb-8 ml-2">
          {{ t('title') }}
        </h1>

        <!-- Mobile Stepper -->
        <div class="lg:hidden mb-6">
          <UStepper
            ref="stepper"
            v-model="currentStep"
            :items="stepperItems"
            color="success"
            class="w-full"
          >
            <!-- Products Step -->
            <template #products>
              <div class="space-y-6">
                <!-- Search Section -->
                <div class="mb-6">
                  <div class="flex gap-3">
                    <div class="relative flex-1">
                      <UInput
                        v-model="searchQuery"
                        :placeholder="t('search_placeholder')"
                        icon="i-lucide-search"
                        class="flex-1 w-full"
                        size="lg"
                      />

                      <!-- Search Results Dropdown -->
                      <div v-if="searchResults.length > 0" class="absolute top-full left-0 right-0 z-10 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto">
                        <div
                          v-for="product in searchResults"
                          :key="product.id"
                          class="flex items-center gap-3 p-3 hover:bg-gray-50 cursor-pointer"
                        >
                          <!-- Product Image -->
                          <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
                            <img
                              :src="product.image"
                              :alt="product.name"
                              class="w-full h-full object-cover rounded-lg"
                              @error="($event.target as HTMLImageElement).style.display = 'none'"
                            >
                          </div>

                          <!-- Product Info -->
                          <div class="flex-1 min-w-0">
                            <h4 class="text-sm font-medium text-gray-900 truncate">
                              {{ product.name }}
                            </h4>
                            <p class="text-xs text-gray-500">
                              {{ getCategoryName(product.category) }}
                            </p>
                          </div>

                          <!-- Add to Order Button -->
                          <UButton
                            :label="t('add_to_order')"
                            color="success"
                            variant="solid"
                            size="sm"
                            @click="showQuantitySelector(product)"
                          />
                        </div>
                      </div>

                      <!-- Quantity Selector Dropdown -->
                      <div v-if="selectedProduct" class="absolute top-full left-0 right-0 z-20 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                        <div class="p-4">
                          <div class="flex items-center gap-3 mb-4">
                            <!-- Product Image -->
                            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
                              <img
                                :src="selectedProduct.image"
                                :alt="selectedProduct.name"
                                class="w-full h-full object-cover rounded-lg"
                                @error="($event.target as HTMLImageElement).style.display = 'none'"
                              >
                            </div>

                            <!-- Product Info -->
                            <div class="flex-1 min-w-0">
                              <h4 class="text-sm font-medium text-gray-900 truncate">
                                {{ selectedProduct.name }}
                              </h4>
                              <p class="text-xs text-gray-500">
                                {{ getCategoryName(selectedProduct.category) }}
                              </p>
                            </div>

                            <!-- Quantity Controls -->
                            <div class="flex items-center gap-3">
                              <UButton
                                icon="i-lucide-minus"
                                variant="outline"
                                color="primary"
                                size="sm"
                                @click="decreaseSelectedQuantity"
                              />
                              <span class="w-8 text-center text-sm font-medium">
                                {{ selectedQuantity }}
                              </span>
                              <UButton
                                icon="i-lucide-plus"
                                variant="outline"
                                color="primary"
                                size="sm"
                                @click="increaseSelectedQuantity"
                              />
                            </div>
                          </div>

                          <!-- Action Buttons -->
                          <div class="flex gap-2">
                            <UButton
                              :label="t('cancel')"
                              variant="ghost"
                              color="primary"
                              size="sm"
                              class="flex-1"
                              @click="cancelQuantitySelector"
                            />
                            <UButton
                              :label="t('add_to_order')"
                              color="success"
                              variant="solid"
                              size="sm"
                              class="flex-1"
                              @click="confirmAddToOrder"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <UButton
                      :label="t('find_in_catalog')"
                      color="success"
                      variant="outline"
                      size="lg"
                      @click="findInCatalog"
                    />
                  </div>
                </div>

                <!-- Order Products List -->
                <UCard v-if="orderProducts.length > 0" class="border border-gray-200 shadow-sm">
                  <div class="divide-y divide-gray-200">
                    <div
                      v-for="product in orderProducts"
                      :key="product.id"
                      class="flex items-center gap-3.5 mb-3"
                    >
                      <!-- Product Image -->
                      <div class="w-13 h-13 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
                        <img
                          :src="product.image"
                          :alt="product.name"
                          class="w-full h-full object-cover rounded-lg"
                          @error="($event.target as HTMLImageElement).style.display = 'none'"
                        >
                      </div>

                      <!-- Product Info -->
                      <div class="flex-1 min-w-0">
                        <h3 class="text-sm font-semibold text-gray-900 truncate">
                          {{ product.name }}
                        </h3>
                        <p class="text-xs text-gray-500">
                          {{ getCategoryName(product.category) }}
                        </p>
                      </div>

                      <!-- Quantity Controls -->
                      <div class="flex items-center gap-1 border border-gray-300 rounded-lg p-1">
                        <UButton
                          icon="i-lucide-minus"
                          variant="ghost"
                          color="primary"
                          size="xs"
                          class="w-6 h-6 p-0"
                          @click="decreaseQuantity(product.id)"
                        />
                        <span class="w-6 text-center text-sm font-medium text-gray-900">
                          {{ product.quantity }}
                        </span>
                        <UButton
                          icon="i-lucide-plus"
                          variant="ghost"
                          color="primary"
                          size="xs"
                          class="w-6 h-6 p-0"
                          @click="increaseQuantity(product.id)"
                        />
                      </div>
                    </div>
                  </div>
                </UCard>

                <!-- Empty State -->
                <div v-else class="text-center py-12">
                  <UIcon name="i-lucide-search" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 mb-2">
                    {{ t('find_products_title') }}
                  </h3>
                  <p class="text-gray-500">
                    {{ t('find_products_description') }}
                  </p>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between pt-4">
                  <div />
                  <UButton
                    :label="t('stepper.next')"
                    trailing-icon="i-lucide-arrow-right"
                    color="success"
                    :disabled="!canProceedToNext()"
                    @click="nextStep"
                  />
                </div>
              </div>
            </template>

            <!-- Complete Order Step -->
            <template #complete>
              <div class="space-y-6">
                <h2 class="text-lg font-semibold text-gray-900">
                  {{ t('complete_order') }}
                </h2>

                <!-- Billing Address -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ t('billing_address') }}
                  </label>
                  <USelectMenu
                    v-model="billingAddress"
                    :options="addressOptions"
                    :placeholder="t('select_billing_address')"
                    class="w-full"
                  />
                </div>

                <!-- Delivery Address -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ t('delivery_address') }}
                  </label>
                  <USelectMenu
                    v-model="deliveryAddress"
                    :options="addressOptions"
                    :placeholder="t('select_delivery_address')"
                    class="w-full"
                  />
                </div>

                <!-- PO Number -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ t('po_number') }}
                  </label>
                  <UInput
                    v-model="poNumber"
                    :placeholder="t('po_number_placeholder')"
                    class="w-full"
                  />
                </div>

                <!-- Remarks -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ t('remarks') }}
                  </label>
                  <UTextarea
                    v-model="remarks"
                    :placeholder="t('remarks_placeholder')"
                    :rows="4"
                    class="w-full"
                  />
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3 pt-4">
                  <UButton
                    :label="t('place_order')"
                    color="success"
                    block
                    size="lg"
                    @click="placeOrder"
                  />
                  <UButton
                    :label="t('remove_all_cancel')"
                    variant="ghost"
                    color="primary"
                    block
                    @click="removeAllItems"
                  />
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between pt-4">
                  <UButton
                    :label="t('stepper.back')"
                    leading-icon="i-lucide-arrow-left"
                    variant="outline"
                    @click="prevStep"
                  />
                  <div />
                </div>
              </div>
            </template>
          </UStepper>
        </div>

        <!-- Desktop Layout (existing) -->
        <div class="hidden lg:grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Left Section - Product Search and List -->
          <div class="lg:col-span-2">
            <!-- Search Section -->
            <div class="mb-6">
              <div class="flex gap-3">
                <div class="relative flex-1">
                  <UInput
                    v-model="searchQuery"
                    :placeholder="t('search_placeholder')"
                    icon="i-lucide-search"
                    class="flex-1 w-full"
                    size="lg"
                  />

                  <!-- Search Results Dropdown -->
                  <div v-if="searchResults.length > 0" class="absolute top-full left-0 right-0 z-10 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto">
                    <div
                      v-for="product in searchResults"
                      :key="product.id"
                      class="flex items-center gap-3 p-3 hover:bg-gray-50 cursor-pointer"
                    >
                      <!-- Product Image -->
                      <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
                        <img
                          :src="product.image"
                          :alt="product.name"
                          class="w-full h-full object-cover rounded-lg"
                          @error="($event.target as HTMLImageElement).style.display = 'none'"
                        >
                      </div>

                      <!-- Product Info -->
                      <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-900 truncate">
                          {{ product.name }}
                        </h4>
                        <p class="text-xs text-gray-500">
                          {{ getCategoryName(product.category) }}
                        </p>
                      </div>

                      <!-- Add to Order Button -->
                      <UButton
                        :label="t('add_to_order')"
                        color="success"
                        variant="solid"
                        size="sm"
                        @click="showQuantitySelector(product)"
                      />
                    </div>
                  </div>

                  <!-- Quantity Selector Dropdown -->
                  <div v-if="selectedProduct" class="absolute top-full left-0 right-0 z-20 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                    <div class="p-4">
                      <div class="flex items-center gap-3 mb-4">
                        <!-- Product Image -->
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
                          <img
                            :src="selectedProduct.image"
                            :alt="selectedProduct.name"
                            class="w-full h-full object-cover rounded-lg"
                            @error="($event.target as HTMLImageElement).style.display = 'none'"
                          >
                        </div>

                        <!-- Product Info -->
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 truncate">
                            {{ selectedProduct.name }}
                          </h4>
                          <p class="text-xs text-gray-500">
                            {{ getCategoryName(selectedProduct.category) }}
                          </p>
                        </div>

                        <!-- Quantity Controls -->
                        <div class="flex items-center gap-3">
                          <UButton
                            icon="i-lucide-minus"
                            variant="outline"
                            color="primary"
                            size="sm"
                            @click="decreaseSelectedQuantity"
                          />
                          <span class="w-8 text-center text-sm font-medium">
                            {{ selectedQuantity }}
                          </span>
                          <UButton
                            icon="i-lucide-plus"
                            variant="outline"
                            color="primary"
                            size="sm"
                            @click="increaseSelectedQuantity"
                          />
                        </div>
                      </div>

                      <!-- Action Buttons -->
                      <div class="flex gap-2">
                        <UButton
                          :label="t('cancel')"
                          variant="ghost"
                          color="primary"
                          size="sm"
                          class="flex-1"
                          @click="cancelQuantitySelector"
                        />
                        <UButton
                          :label="t('add_to_order')"
                          color="success"
                          variant="solid"
                          size="sm"
                          class="flex-1"
                          @click="confirmAddToOrder"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <UButton
                  :label="t('find_in_catalog')"
                  color="success"
                  variant="outline"
                  size="lg"
                  @click="findInCatalog"
                />
              </div>
            </div>

            <!-- Order Products List -->
            <UCard v-if="orderProducts.length > 0" class="border border-gray-200 shadow-sm">
              <div class="divide-y divide-gray-200">
                <div
                  v-for="product in orderProducts"
                  :key="product.id"
                  class="flex items-center gap-3.5 mb-3"
                >
                  <!-- Product Image -->
                  <div class="w-13 h-13 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
                    <img
                      :src="product.image"
                      :alt="product.name"
                      class="w-full h-full object-cover rounded-lg"
                      @error="($event.target as HTMLImageElement).style.display = 'none'"
                    >
                  </div>

                  <!-- Product Info -->
                  <div class="flex-1 min-w-0">
                    <h3 class="text-sm font-semibold text-gray-900 truncate">
                      {{ product.name }}
                    </h3>
                    <p class="text-xs text-gray-500">
                      {{ getCategoryName(product.category) }}
                    </p>
                  </div>

                  <!-- Quantity Controls -->
                  <div class="flex items-center gap-1 border border-gray-300 rounded-lg p-1">
                    <UButton
                      icon="i-lucide-minus"
                      variant="ghost"
                      color="primary"
                      size="xs"
                      class="w-6 h-6 p-0"
                      @click="decreaseQuantity(product.id)"
                    />
                    <span class="w-6 text-center text-sm font-medium text-gray-900">
                      {{ product.quantity }}
                    </span>
                    <UButton
                      icon="i-lucide-plus"
                      variant="ghost"
                      color="primary"
                      size="xs"
                      class="w-6 h-6 p-0"
                      @click="increaseQuantity(product.id)"
                    />
                  </div>
                </div>
              </div>
            </UCard>

            <!-- Empty State -->
            <div v-else class="text-center py-12">
              <UIcon name="i-lucide-search" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                {{ t('find_products_title') }}
              </h3>
              <p class="text-gray-500">
                {{ t('find_products_description') }}
              </p>
            </div>
          </div>

          <!-- Right Section - Order Completion -->
          <div class="lg:col-span-1">
            <UCard class="border-0 shadow-sm">
              <div class="space-y-6">
                <h2 class="text-lg font-semibold text-gray-900">
                  {{ t('complete_order') }}
                </h2>

                <!-- Billing Address -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ t('billing_address') }}
                  </label>
                  <USelectMenu
                    v-model="billingAddress"
                    :options="addressOptions"
                    :placeholder="t('select_billing_address')"
                    class="w-full"
                  />
                </div>

                <!-- Delivery Address -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ t('delivery_address') }}
                  </label>
                  <USelectMenu
                    v-model="deliveryAddress"
                    :options="addressOptions"
                    :placeholder="t('select_delivery_address')"
                    class="w-full"
                  />
                </div>

                <!-- PO Number -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ t('po_number') }}
                  </label>
                  <UInput
                    v-model="poNumber"
                    :placeholder="t('po_number_placeholder')"
                    class="w-full"
                  />
                </div>

                <!-- Remarks -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ t('remarks') }}
                  </label>
                  <UTextarea
                    v-model="remarks"
                    :placeholder="t('remarks_placeholder')"
                    :rows="4"
                    class="w-full"
                  />
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3 pt-4">
                  <UButton
                    :label="t('place_order')"
                    color="success"
                    block
                    size="lg"
                    @click="placeOrder"
                  />
                  <UButton
                    :label="t('remove_all_cancel')"
                    variant="ghost"
                    color="primary"
                    block
                    @click="removeAllItems"
                  />
                </div>
              </div>
            </UCard>
          </div>
        </div>
      </UContainer>
    </div>
  </div>
</template>
