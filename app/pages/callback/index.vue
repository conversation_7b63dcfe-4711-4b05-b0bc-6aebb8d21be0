<script setup lang="ts">
import { useMyAuth } from '~/composables/auth/useMyAuth'

definePageMeta({
  layout: false,
})

const { handleRedirectCallback } = useMyAuth()

onMounted(async () => {
  try {
    if (useUserStore().isLoggedIn) {
      navigateTo('/')
      return
    }

    await handleRedirectCallback()
    navigateTo('/')
  }
  catch {
    navigateTo('/errors/authorization-failed')
  }
})
</script>
