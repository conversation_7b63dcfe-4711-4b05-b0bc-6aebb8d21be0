<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

definePageMeta({
  layout: 'default',
})

const { t } = useI18n()

// Form data (static for now)
const userInfo = ref({
  name: '<PERSON>',
  email: '<EMAIL>',
  language: 'English',
})

// Success message state
const showSuccessMessage = ref(false)

// Methods
function saveUserInfo() {
  // Show success message
  showSuccessMessage.value = true

  // Hide success message after 3 seconds
  setTimeout(() => {
    showSuccessMessage.value = false
  }, 3000)
}
</script>

<template>
  <div class="max-w-2xl mx-auto p-6">
    <!-- My Info Section -->
    <div>
      <h1 class="text-2xl font-semibold text-gray-900 mb-8">
        {{ t('settings.my_info.title') }}
      </h1>

      <div class="space-y-2">
        <!-- Name Field -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ t('settings.my_info.name') }}
          </label>
          <UInput
            v-model="userInfo.name"
            size="lg"
            class="w-full"
          />
        </div>

        <!-- Email Field -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ t('settings.my_info.email_address') }}
          </label>
          <UInput
            v-model="userInfo.email"
            type="email"
            size="lg"
            class="w-full"
          />
        </div>

        <!-- Language Field -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ t('settings.my_info.language') }}
          </label>
          <USelectMenu
            v-model="userInfo.language"
            size="lg"
            class="w-full"
          />
        </div>

        <!-- Save Button -->
        <div class="pt-4">
          <UButton
            size="lg"
            class="bg-green-600 hover:bg-green-700 text-white px-3 py-1.5 rounded-lg font-medium"
            @click="saveUserInfo"
          >
            {{ t('settings.my_info.save') }}
          </UButton>
        </div>

        <!-- Success Message -->
        <div v-if="showSuccessMessage" class="pt-4">
          <UAlert
            color="primary"
            variant="soft"
            :title="t('settings.my_info.success_message')"
            icon="i-lucide-check"
          />
        </div>
      </div>
    </div>
  </div>
</template>
