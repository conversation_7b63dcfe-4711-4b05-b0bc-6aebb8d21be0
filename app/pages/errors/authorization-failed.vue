<script setup lang="ts">
import { motion } from 'motion-v'
import { useMyAuth } from '~/composables/auth/useMyAuth'

definePageMeta({
  layout: 'auth',
})

const { login } = useMyAuth()
</script>

<template>
  <div class="w-full h-full flex flex-col items-center justify-center" style="min-height: 100vh;">
    <UCard>
      <template #header>
        <h2 class="text-2xl font-bold text-center">
          Authentication Failed
        </h2>
      </template>

      <div>
        <p class="text-center">
          You are not authorized to access this page. Please check your credentials or contact support.
        </p>
        <p class="text-center">
          Please try logging in again.
        </p>
        <p class="text-center">
          <motion.div
            as-child
            :while-hover="{ scale: 1.05 }"
            :while-press="{ scale: 0.95 }"
          >
            <UButton class="mt-4 cursor-pointer" size="xl" @click="login">
              Login
            </UButton>
          </motion.div>
        </p>
      </div>
    </UCard>
  </div>
</template>
