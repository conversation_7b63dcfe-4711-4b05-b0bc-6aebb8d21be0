<script setup lang="ts">
import type { Order } from '~/types/order/order.type'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import OrdersEmptyState from '~/components/dashboard/OrdersEmptyState.vue'
import OrdersTableHeader from '~/components/dashboard/OrdersTableHeader.vue'
import OrdersTableRow from '~/components/dashboard/OrdersTableRow.vue'
import { mockOrders } from '~/utils/mockOrdersData'

definePageMeta({
  layout: 'default',
})

const { t } = useI18n()

// Reactive state
const selectedClient = ref<string>('')
const selectedDate = ref<string>('')
const selectedStatus = ref<string>('')
const searchQuery = ref('')

// Mock orders data - would come from API
const orders = ref<Order[]>(mockOrders)
const isLoading = ref(false)
</script>

<template>
  <!-- Filters Section - Fuera del UCard -->
  <div class="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between mb-6">
    <div class="flex flex-col sm:flex-row gap-3 flex-1 mt-4 ml-10">
      <!-- Client Filter -->
      <USelectMenu v-model="selectedClient" :options="[]" :placeholder="t('filters.client')" class="w-full sm:w-22" />

      <!-- Date Filter -->
      <USelectMenu v-model="selectedDate" :options="[]" :placeholder="t('filters.date')" class="w-full sm:w-22" />

      <!-- Status Filter -->
      <USelectMenu v-model="selectedStatus" :options="[]" :placeholder="t('filters.status')" class="w-full sm:w-22" />
    </div>

    <!-- Search and Actions -->
    <div class="flex gap-2 w-full sm:w-auto mr-10 mt-4">
      <UInput
        v-model="searchQuery" :placeholder="t('search_placeholder')" icon="i-lucide-search"
        class="flex-1 sm:w-64"
      />
    </div>
  </div>

  <!-- Orders Table -->
  <UCard v-if="isLoading" class="p-12">
    <div class="flex justify-center">
      <UIcon name="i-lucide-loader-2" class="animate-spin" size="40" />
    </div>
  </UCard>

  <OrdersEmptyState v-else-if="orders.length === 0" />

  <UCard v-else class="overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <OrdersTableHeader />
        <tbody class="bg-white divide-y divide-gray-200">
          <OrdersTableRow v-for="order in orders" :key="order.id + order.date" :order="order" />
        </tbody>
      </table>
    </div>
  </UCard>
</template>
