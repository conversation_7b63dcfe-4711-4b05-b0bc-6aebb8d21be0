<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { categories, products } from '~/utils/mockProductsData'

definePageMeta({
  layout: 'default',
})

const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// State
const selectedCategory = ref('all')
const searchQuery = ref('')
const page = ref(Number.parseInt(route.query.page as string || '1'))
const itemsPerPage = ref(12)
const mobileCategoriesOpen = ref(false)

// Force recalculation when page changes
const forceUpdateKey = ref(0)

// Update URL and force update when page changes
watch(page, (newPage) => {
  // Force immediate recalculation
  forceUpdateKey.value++
  // Update URL (this is asynchronous)
  router.push({
    query: {
      ...route.query,
      page: newPage.toString(),
    },
  })
}, { immediate: false })

// Update page when URL changes
watch(() => route.query.page, (newPage) => {
  if (newPage) {
    page.value = Number.parseInt(newPage as string)
  }
  else {
    page.value = 1
  }
}, { immediate: true })

// Additional watch to debug page changes
watch(page, (newVal, oldVal) => {
  console.warn('⭐ Page value changed:', oldVal, '->', newVal)
}, { immediate: true })

// Computed properties
const selectedCategoryName = computed(() => {
  if (selectedCategory.value === 'recently-ordered') {
    return t('categories.recently_ordered')
  }

  const category = categories.find(c => c.slug === selectedCategory.value)
  return category ? category.name : t('categories.all_products')
})

const filteredProducts = computed(() => {
  let filtered = [...products]

  // Filter by category
  if (selectedCategory.value === 'recently-ordered') {
    filtered = filtered.filter(product => product.recently_ordered)
  }
  else if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(product => product.category === selectedCategory.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(product =>
      product.name.toLowerCase().includes(query)
      || product.cnk.includes(query),
    )
  }

  return filtered
})

watch(page, () => {
  forceUpdateKey.value++
}, { immediate: false })

const displayedProducts = computed(() => {
  // Include the forceUpdateKey in the dependency tracking
  const _ = forceUpdateKey.value
  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  const products = filteredProducts.value.slice(start, end)
  return products
})

// Methods
function onSelectCategory(category: string) {
  selectedCategory.value = category
  mobileCategoriesOpen.value = false
  router.push({
    query: {
      ...route.query,
      category,
      page: '1',
    },
  })
}

function onSearch(query: string) {
  searchQuery.value = query
  router.push({
    query: {
      ...route.query,
      search: query,
      page: '1',
    },
  })
}

function onFilter(_filters: any) {
  router.push({
    query: {
      ...route.query,
      page: '1',
    },
  })
}

// Log which products are being shown at a specific time (for debugging purposes)
function _debugDisplayedProducts() {
  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  console.warn('🔍 CURRENT DISPLAYED PRODUCTS:')
  console.warn('- Page:', page.value)
  console.warn('- Range:', start, 'to', end)
  console.warn('- Products:', filteredProducts.value.slice(start, end).map(p => p.id))
}

// Lifecycle
onMounted(() => {
  // Initialize values from URL
  if (route.query.category) {
    selectedCategory.value = route.query.category as string
  }
  if (route.query.search) {
    searchQuery.value = route.query.search as string
  }
})
</script>

<template>
  <UContainer class="py-10">
    <ProductFilters @search="onSearch" @filter="onFilter" />

    <div class="flex flex-col md:flex-row">
      <!-- Category sidebar - Responsive behavior -->
      <div class="w-full md:w-64 md:shrink-0 mb-6 md:mb-0">
        <div class="mb-4 md:hidden">
          <UButton
            block
            color="neutral"
            variant="soft"
            :icon="mobileCategoriesOpen ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'"
            @click="mobileCategoriesOpen = !mobileCategoriesOpen"
          >
            {{ mobileCategoriesOpen ? t('categories.hide_categories') : t('categories.show_categories') }}
          </UButton>
        </div>

        <div :class="{ 'hidden md:block': !mobileCategoriesOpen }">
          <ProductCategorySidebar
            :selected-category="selectedCategory"
            :show-recently-ordered="true"
            @select-category="onSelectCategory"
          />
        </div>
      </div>

      <!-- Product grid -->
      <div class="flex-1 md:pl-6">
        <h2 class="text-lg font-semibold mb-4">
          {{ selectedCategoryName }}
        </h2>

        <div
          :key="`product-grid-${forceUpdateKey}`"
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 p-4"
        >
          <div v-for="product in displayedProducts" :key="`product-${product.id}`" class="h-full">
            <ProductCard :product="product" />
          </div>
        </div>

        <!-- Pagination -->
        <ProductPagination
          v-model:page="page"
          :total-items="filteredProducts.length"
          :items-per-page="itemsPerPage"
        />
      </div>
    </div>
  </UContainer>
</template>
