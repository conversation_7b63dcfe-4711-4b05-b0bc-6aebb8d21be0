<script setup lang="ts">
import QuickActions from '../../components/dashboard/QuickActions.vue'
import RecentOrders from '../../components/dashboard/RecentOrders.vue'
import { mockOrders } from '../../utils/mockOrdersData'

const orderHistory = ref(mockOrders)
const isLoading = ref(false)

// Mock function to simulate fetching orders
function fetchOrders() {
  isLoading.value = true
  try {
    // In a real app, this would be an API call
    // Using mock data
    orderHistory.value = mockOrders
  }
  catch (error) {
    console.error('Error fetching orders:', error)
  }
  finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchOrders()
})
</script>

<template>
  <div>
    <QuickActions />
    <RecentOrders :orders="orderHistory" :is-loading="isLoading" />
  </div>
</template>
