# Hyperfox - Order Portal - Project Overview

The Hyperfox Business Portal is a modular, developer-friendly B2B storefront built with Nuxt 3, Pinia, and TanStack Table. Designed for fast setup and seamless integration, it helps standardize incoming orders, reducing manual errors and overhead.

---

## Setup

Before starting development, ensure you have the following installed:

### 1. Install PNPM (if not already installed)

We use [PNPM](https://pnpm.io/) for faster, disk-efficient package management.

```
# Install globally via npm
npm install -g pnpm
```

To confirm it's installed:

```
pnpm --version
```

### 2. Install project dependencies

```
pnpm install
```

### 3. Create Local HTTPS Certificates

To support PKCE in local development, HTTPS is required.

Install `mkcert` and generate local certificates:

```
brew install mkcert
```
```
mkcert -install
```
```
mkdir -p certs
```
```
mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost-cert.pem transport.hyperfox.local transport.api.hyperfox.local
```

### 4. Start the development server

```
pnpm dev
```

---

## Project Description

The Hyperfox Business Portal is a modular and easy-to-deploy B2B e-commerce platform tailored to streamline business-to-business transactions. It is part of Hyperfox’s broader automation suite, aiming to reduce manual order processing through standardization and integration with existing ERP or CRM systems.

This portal is ideal for companies with recurring or high-volume ordering workflows. It allows businesses to digitize their ordering flow, improve accuracy, and save time—without compromising flexibility or branding requirements.

---

## Key Technologies

To build a scalable, maintainable, and developer-friendly system, we selected the following tools:

### Core Technologies

- **Nuxt 3**: A modern Vue.js framework with built-in TypeScript support, great DX.
- **TanStack Table (Vue)**: Provides highly customizable table features like sorting, filtering, and pagination with a lightweight footprint.
- **ESLint (antfu config)**: Ensures clean, consistent code using widely adopted linting rules that require minimal manual configuration.
- **@nuxtjs/i18n**: Internationalization for Nuxt Applications

### UX/UI Technologies

- **Nuxt UI Pro**: A flexible, accessible component library designed for Nuxt, offering first-class Tailwind integration.
- **Motion**: Provides powerful, declarative animations to improve UI interactivity (based on Motion One).

### State Management

- **Pinia**: A modular, type-safe store system that scales well with larger apps.
- **TanStack Query**: Handles caching, server state management, and API synchronization effortlessly.

### Testing

- **Vitest**: A blazing-fast Vite-native unit test runner.
- **Vue Testing Library**: Promotes testing components from the user's perspective.
- **@nuxt/test-utils**: Initializes full Nuxt app context during unit tests.
- **Playwright**: Enables full end-to-end (E2E) tests in real browsers to simulate real user behavior.
- **Mock Service Worker (MSW)**: Intercepts network requests for test realism and speed without needing a backend.
- **Pinia Testing Utilities**: Provides tools to isolate and test store logic independently.

---

## Architecture Overview

The Hyperfox Business Portal is structured to promote modularity, maintainability, and strong separation of concerns.

### 1. Client (Frontend)
- **Nuxt 3 + Vue 3**: For app logic, routing, and rendering (SSG/SSR).
- **Nuxt UI Pro**: For highly customizable and accessible components.
- **TanStack Table**: For advanced data tables and UI responsiveness.
- **Pinia + TanStack Query**: For robust, decoupled state and server state management.
- **Framer Motion**: For seamless and performant UI animations.

### 2. API Layer
- **Composable API Handlers**: Abstracted fetch logic for reuse and testability.
- **MSW (in dev/test)**: Simulates backend behavior to test complex flows without backend dependency.

### 3. CI/CD & DevOps
- **Linting**: ESLint + antfu config
- **Testing**: Vitest, Cypress, MSW
- **CI/CD**: GitLab CI

---

## Security

- Nuxt route middleware for authenticated access.
- Supports common auth patterns (OAuth2).

---

## Accessibility & Internationalization

- Nuxt UI Pro components follow a11y best practices.
- i18n setup available using `@nuxtjs/i18n`.
