import { resolve } from 'node:path'
import { defineVitestConfig } from '@nuxt/test-utils/config'
import { config as loadEnv } from 'dotenv'

loadEnv({ path: resolve(__dirname, '.env.test') })

export default defineVitestConfig({
  test: {
    include: ['**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules', '.output', 'dist', '.nuxt'],
    environment: 'nuxt',
    globals: true,
    setupFiles: ['./test/test.setup.ts'],
    environmentOptions: {
      nuxt: {
        domEnvironment: 'happy-dom',
        mock: {
          intersectionObserver: true,
          indexedDb: true,
        },
      },
    },
  },
})
