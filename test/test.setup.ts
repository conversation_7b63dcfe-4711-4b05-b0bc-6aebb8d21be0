import { setup } from '@nuxt/test-utils'
import { setupServer } from 'msw/node'
import { afterAll, afterEach, beforeAll } from 'vitest'
import { authServiceHandlers } from '~/api/auth/services/auth-service.handlers'
import { centralTenantServiceHandlers } from '~/api/tenant/central-tenant-service.handlers'
import { useTenant } from '~/composables/tenant/useTenant'

export const mockServer = setupServer(
  ...authServiceHandlers,
  ...centralTenantServiceHandlers,
)

beforeAll(async () => {
  mockServer.listen()
  await setup({ testDir: __dirname })

  const { initializeTenant } = useTenant()
  await initializeTenant()
})

afterEach(() => mockServer.resetHandlers())
afterAll(() => mockServer.close())
