{"name": "art-challenge", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "lint": "eslint", "lint:fix": "eslint --fix", "test": "vitest run", "test:watch": "vitest", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "prepare": "husky install", "postinstall": "husky install"}, "dependencies": {"@lukemorales/query-key-factory": "^1.3.4", "@nuxt/devtools": "^2.4.0", "@nuxt/eslint": "^1.3.1", "@nuxt/fonts": "^0.11.3", "@nuxt/ui-pro": "^3.1.1", "@nuxtjs/i18n": "^9.5.4", "@pinia/nuxt": "^0.11.0", "@tanstack/vue-query": "^5.76.0", "@vee-validate/zod": "^4.15.0", "crypto-js": "^4.2.0", "motion-v": "1.0.0-beta.2", "nuxt": "^3.17.3", "pinia": "^3.0.2", "tailwindcss": "^4.1.6", "vite": "^6.3.5", "vue": "^3.5.13", "vue-router": "^4.5.1", "zod": "^3.24.4"}, "devDependencies": {"@antfu/eslint-config": "^4.13.0", "@nuxt/test-utils": "^3.18.0", "@pinia/testing": "^1.0.1", "@vue/test-utils": "^2.4.6", "eslint": "^9.23.0", "happy-dom": "^17.4.7", "husky": "^9.1.7", "lint-staged": "^15.5.2", "msw": "^2.8.2", "nuxi": "^3.25.1", "playwright-core": "^1.52.0", "typescript": "^5.8.3", "vitest": "^3.1.3"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix", "eslint"]}}