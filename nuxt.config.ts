import { defineNuxtConfig } from 'nuxt/config'

export default defineNuxtConfig({
  future: {
    compatibilityVersion: 4,
  },
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: [
    '@nuxt/eslint',
    '@nuxt/devtools',
    '@nuxt/fonts',
    '@nuxt/ui-pro',
    'motion-v/nuxt',
    '@pinia/nuxt',
    '@nuxtjs/i18n',
    '@nuxt/test-utils/module',
  ],
  plugins: [
    { src: '~/plugins/base64.ts' },
    { src: '~/plugins/tenant-init.client.ts' },
  ],
  css: ['~/assets/css/main.css'],
  ssr: false,
  colorMode: {
    preference: 'light',
  },
  i18n: {
    lazy: true,
    langDir: 'locales/',
    strategy: 'no_prefix',
    locales: [
      { code: 'en', name: 'English', files: ['en/common.json', 'en/auth.json', 'en/header.json', 'en/dashboard.json', 'en/products.json', 'en/settings.json', 'en/orders.json', 'en/quick-order.json'] },
      { code: 'nl', name: 'Nederlands', files: ['nl/common.json', 'nl/auth.json', 'nl/header.json', 'nl/dashboard.json', 'nl/products.json', 'nl/settings.json', 'nl/orders.json', 'nl/quick-order.json'] },
    ],
    defaultLocale: 'en',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
      alwaysRedirect: false,
    },
  },
  runtimeConfig: {
    public: {
      oauthClientId: import.meta.env.NUXT_PUBLIC_OAUTH_CLIENT_ID,
      host: import.meta.env.NUXT_HOST,
      testHostName: import.meta.env.TEST_HOST_NAME,
      nodeEnv: import.meta.env.NODE_ENV,
    },
  },
  devServer: {
    host: import.meta.env.DEV_HOST || 'localhost',
    https: {
      key: './certs/localhost-key.pem',
      cert: './certs/localhost-cert.pem',
    },
  },
})
